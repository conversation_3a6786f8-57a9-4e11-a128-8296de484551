import torch
from 标准 import nerbilstm
from char_data_loader import CharNERDataset
import json
from typing import List, Tuple, Dict

class CharNERPredictor:
    """字符级别NER预测器"""
    
    def __init__(self, model_path: str):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.load_model(model_path)
    
    def load_model(self, model_path: str):
        """加载训练好的字符级别模型"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # 加载模型配置
            self.config = checkpoint['config']
            self.char2idx = checkpoint['char2idx']
            self.label2idx = checkpoint['label2idx']
            self.idx2char = checkpoint['idx2char']
            self.idx2label = checkpoint['idx2label']
            
            # 创建模型
            self.model = nerbilstm(
                vocab_size=checkpoint['vocab_size'],
                embedding_dim=self.config['embedding_dim'],
                hidden_dim=self.config['hidden_dim'],
                output_dim=checkpoint['num_labels'],
                n_layers=self.config['n_layers'],
                bidirectional=self.config['bidirectional'],
                dropout=self.config['dropout']
            )
            
            # 加载模型参数
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            print(f"字符级别模型加载成功！")
            print(f"字符表大小: {checkpoint['vocab_size']}")
            print(f"标签数量: {checkpoint['num_labels']}")
            
        except FileNotFoundError:
            print(f"模型文件 {model_path} 不存在，请先训练模型")
            raise
    
    def predict(self, text: str) -> List[Tuple[str, str]]:
        """对输入文本进行字符级别NER预测"""
        # 转换为字符列表
        chars = list(text)
        
        # 转换为索引
        char_ids = [self.char2idx.get(char, self.char2idx['<UNK>']) for char in chars]
        
        # 填充到最大长度
        max_len = self.config['max_len']
        if len(char_ids) < max_len:
            char_ids.extend([self.char2idx['<PAD>']] * (max_len - len(char_ids)))
        else:
            char_ids = char_ids[:max_len]
        
        # 转换为tensor
        input_tensor = torch.tensor([char_ids]).to(self.device)
        
        with torch.no_grad():
            outputs = self.model(input_tensor)
            predictions = outputs.argmax(dim=-1).cpu().numpy()[0]
        
        # 解码结果
        result = []
        for i, char in enumerate(chars):
            if i < len(predictions):
                label = self.idx2label[predictions[i]]
                result.append((char, label))
        
        return result
    
    def extract_entities(self, char_label_pairs: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        """从字符标签对中提取实体"""
        entities = {'PER': [], 'ORG': [], 'LOC': []}
        current_entity = ""
        current_type = None
        
        for char, label in char_label_pairs:
            if label.startswith('B-'):
                # 开始新实体
                if current_entity and current_type:
                    entities[current_type].append(current_entity)
                current_entity = char
                current_type = label[2:]  # 去掉'B-'前缀
            elif label.startswith('I-') and current_type == label[2:]:
                # 继续当前实体
                current_entity += char
            else:
                # 结束当前实体
                if current_entity and current_type:
                    entities[current_type].append(current_entity)
                current_entity = ""
                current_type = None
        
        # 处理最后一个实体
        if current_entity and current_type:
            entities[current_type].append(current_entity)
        
        return entities

def evaluate_on_test_data():
    """在测试数据上评估字符级别模型"""
    try:
        predictor = CharNERPredictor('model/best_char_ner.pth')
    except:
        print("模型加载失败，请先训练字符级别模型")
        return
    
    # 读取测试数据
    test_sentences = [
        "马云是阿里巴巴的创始人，总部位于杭州。",
        "乔布斯曾在苹果公司工作，他的办公室设在加利福尼亚州的库比蒂诺。",
        "比尔·盖茨和保罗·艾伦一起创立了微软公司，公司总部设在华盛顿州的雷德蒙德。",
        "埃隆·马斯克是特斯拉和SpaceX的首席执行官，他常常在加利福尼亚州出差。",
        "李彦宏在百度公司担任首席执行官，公司总部位于北京市。"
    ]
    
    print("\n=== 字符级别NER识别结果 ===")
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n句子 {i}: {sentence}")
        
        # 预测
        char_label_pairs = predictor.predict(sentence)
        
        # 提取实体
        entities = predictor.extract_entities(char_label_pairs)
        
        # 显示结果
        all_entities = []
        type_map = {'PER': '人名', 'ORG': '机构', 'LOC': '地点'}
        
        for entity_type, entity_list in entities.items():
            for entity in entity_list:
                all_entities.append(f"{entity}({type_map[entity_type]})")
        
        if all_entities:
            print(f"识别的实体: {', '.join(all_entities)}")
        else:
            print("未识别到实体")
        
        # 显示详细标注（前20个字符）
        print("字符标注详情:")
        display_pairs = char_label_pairs[:30]  # 只显示前30个字符
        for char, label in display_pairs:
            if label != 'O':
                print(f"  '{char}' -> {label}")
            else:
                print(f"  '{char}' -> O")

def interactive_test():
    """交互式字符级别测试"""
    try:
        predictor = CharNERPredictor('model/best_char_ner.pth')
    except:
        print("模型加载失败，请先训练字符级别模型")
        return
    
    print("\n=== 交互式字符级别NER测试 ===")
    print("输入文本进行实体识别（输入'quit'退出）:")
    
    while True:
        text = input("\n请输入文本: ").strip()
        if text.lower() == 'quit':
            break
        
        if not text:
            continue
        
        # 预测
        char_label_pairs = predictor.predict(text)
        
        # 提取实体
        entities = predictor.extract_entities(char_label_pairs)
        
        print(f"\n原文: {text}")
        print("字符级别标注:")
        for char, label in char_label_pairs:
            if label == 'O':
                print(f"  '{char}' -> O")
            else:
                print(f"  '{char}' -> {label}")
        
        # 显示提取的实体
        type_map = {'PER': '人名', 'ORG': '机构', 'LOC': '地点'}
        has_entities = False
        
        print("\n识别的实体:")
        for entity_type, entity_list in entities.items():
            if entity_list:
                has_entities = True
                type_name = type_map[entity_type]
                print(f"  {type_name}: {', '.join(entity_list)}")
        
        if not has_entities:
            print("  未识别到实体")

def compare_char_vs_word():
    """比较字符级别和词级别的识别效果"""
    print("\n=== 字符级别 vs 词级别 NER比较 ===")
    
    test_sentence = "马云在杭州创立了阿里巴巴公司。"
    print(f"测试句子: {test_sentence}")
    
    # 字符级别预测
    try:
        char_predictor = CharNERPredictor('model/best_char_ner.pth')
        char_pairs = char_predictor.predict(test_sentence)
        char_entities = char_predictor.extract_entities(char_pairs)
        
        print(f"\n字符级别识别:")
        print("字符标注:", char_pairs[:len(test_sentence)])
        print("提取实体:", char_entities)
        
    except Exception as e:
        print(f"字符级别预测失败: {e}")
    
    print(f"\n词级别对比:")
    print("分词结果: ['马云', '在', '杭州', '创立', '了', '阿里巴巴', '公司', '。']")
    print("词级标注: [('马云', 'B-PER'), ('在', 'O'), ('杭州', 'B-LOC'), ...]")
    print("提取实体: {'PER': ['马云'], 'LOC': ['杭州'], 'ORG': ['阿里巴巴']}")

def main():
    """主函数"""
    print("字符级别NER模型评估")
    print("选择评估模式:")
    print("1. 在测试数据上评估")
    print("2. 交互式测试")
    print("3. 字符级别vs词级别比较")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == '1':
        evaluate_on_test_data()
    elif choice == '2':
        interactive_test()
    elif choice == '3':
        compare_char_vs_word()
    else:
        print("无效选择!")

if __name__ == "__main__":
    main()