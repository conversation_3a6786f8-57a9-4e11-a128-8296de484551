# BiLSTM命名实体识别(NER)项目

这是一个基于双向LSTM的中文命名实体识别项目，可以识别人名、机构名和地点名。

## 项目结构

```
├── 标准.py              # BiLSTM模型定义
├── data_processor.py    # 数据处理类
├── train.py            # 训练脚本
├── evaluate.py         # 评估和测试脚本
├── 标注测试.txt         # 测试数据
├── requirements.txt    # 依赖包
└── README.md          # 项目说明
```

## 功能特点

- **双向LSTM架构**: 利用上下文信息进行更准确的实体识别
- **中文分词支持**: 使用jieba进行中文分词
- **多实体类型**: 支持人名(PER)、机构名(ORG)、地点名(LOC)识别
- **灵活配置**: 可调整模型参数和超参数
- **交互式测试**: 支持实时输入测试

## 模型架构

```
输入文本 -> 分词 -> 词嵌入 -> BiLSTM -> 全连接层 -> 实体标签
```

### 模型参数
- **嵌入维度**: 100
- **隐藏层维度**: 128  
- **LSTM层数**: 2
- **双向**: True
- **Dropout**: 0.3

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 训练模型

```bash
python train.py
```

训练过程中会显示：
- 训练和验证损失
- 准确率指标
- 最佳模型自动保存

### 3. 评估模型

```bash
python evaluate.py
```

选择评估模式：
- 模式1: 在测试数据上评估
- 模式2: 交互式测试

## 使用示例

### 训练示例
```python
from train import train_model
model, processor = train_model()
```

### 预测示例
```python
from evaluate import load_model
from train import test_model

# 加载模型
model = load_model('best_ner_model.pth', vocab_size)

# 预测
text = "马云是阿里巴巴的创始人，总部位于杭州。"
result = test_model(model, processor, text)

# 结果: [('马云', 'B-PER'), ('阿里巴巴', 'B-ORG'), ('杭州', 'B-LOC')]
```

## 标注格式

使用BIO标注格式：
- **B-PER**: 人名开始
- **I-PER**: 人名内部
- **B-ORG**: 机构名开始  
- **I-ORG**: 机构名内部
- **B-LOC**: 地点名开始
- **I-LOC**: 地点名内部
- **O**: 非实体

## 性能优化建议

1. **数据增强**: 增加更多标注数据
2. **预训练词向量**: 使用Word2Vec或BERT词向量
3. **模型融合**: 结合CRF层提升序列标注性能
4. **超参数调优**: 调整学习率、批次大小等
5. **正则化**: 添加L2正则化防止过拟合

## 扩展功能

- [ ] 添加CRF层
- [ ] 支持更多实体类型
- [ ] 集成预训练词向量
- [ ] Web API接口
- [ ] 可视化分析工具

## 注意事项

1. 当前使用简单规则生成标注数据，实际应用需要人工标注
2. 模型在小数据集上训练，实际使用需要更大规模数据
3. 中文分词质量会影响最终识别效果
4. 建议在GPU上训练以提升速度