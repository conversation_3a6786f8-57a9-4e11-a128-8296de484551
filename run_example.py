"""
BiLSTM NER项目运行示例
"""

def main():
    print("=== BiLSTM命名实体识别项目 ===\n")
    
    print("项目文件已创建完成!")
    print("\n项目包含以下文件:")
    print("📝 标准.py - BiLSTM模型定义")
    print("📝 data_processor.py - 数据处理")  
    print("📝 train.py - 训练脚本")
    print("📝 evaluate.py - 评估脚本")
    print("📝 标注测试.txt - 测试数据")
    print("📝 requirements.txt - 依赖包")
    print("📝 README.md - 项目说明")
    
    print("\n🚀 运行步骤:")
    print("1. 安装依赖: pip install -r requirements.txt")
    print("2. 训练模型: python train.py")
    print("3. 评估模型: python evaluate.py")
    
    print("\n🎯 模型特点:")
    print("• 双向LSTM架构，利用上下文信息")
    print("• 支持中文分词和实体识别")
    print("• 可识别人名、机构名、地点名")
    print("• 包含训练、评估和交互测试功能")
    
    print("\n📊 预期识别效果:")
    test_examples = [
        "马云是阿里巴巴的创始人，总部位于杭州。",
        "乔布斯曾在苹果公司工作，他的办公室设在加利福尼亚州的库比蒂诺。",
        "李彦宏在百度公司担任首席执行官，公司总部位于北京市。"
    ]
    
    for i, text in enumerate(test_examples, 1):
        print(f"\n示例{i}: {text}")
        # 模拟识别结果
        if "马云" in text:
            print("  识别结果: 马云(人名), 阿里巴巴(机构), 杭州(地点)")
        elif "乔布斯" in text:
            print("  识别结果: 乔布斯(人名), 苹果公司(机构), 加利福尼亚州(地点), 库比蒂诺(地点)")
        elif "李彦宏" in text:
            print("  识别结果: 李彦宏(人名), 百度公司(机构), 北京市(地点)")

if __name__ == "__main__":
    main()