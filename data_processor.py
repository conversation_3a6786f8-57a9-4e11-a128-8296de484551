import torch
from torch.utils.data import Dataset, DataLoader
import jieba
import re
from collections import Counter
from typing import List, Tuple, Dict

class NERDataset(Dataset):
    def __init__(self, texts: List[str], labels: List[List[str]], word2idx: Dict, label2idx: Dict, max_len: int = 128):
        self.texts = texts
        self.labels = labels
        self.word2idx = word2idx
        self.label2idx = label2idx
        self.max_len = max_len
        
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = self.texts[idx]
        labels = self.labels[idx]
        
        # 分词
        words = list(jieba.cut(text))
        
        # 转换为索引
        word_ids = [self.word2idx.get(word, self.word2idx['<UNK>']) for word in words]
        label_ids = [self.label2idx.get(label, self.label2idx['O']) for label in labels[:len(words)]]
        
        # 填充或截断
        if len(word_ids) < self.max_len:
            word_ids.extend([self.word2idx['<PAD>']] * (self.max_len - len(word_ids)))
            label_ids.extend([self.label2idx['O']] * (self.max_len - len(label_ids)))
        else:
            word_ids = word_ids[:self.max_len]
            label_ids = label_ids[:self.max_len]
            
        return torch.tensor(word_ids), torch.tensor(label_ids)

class NERDataProcessor:
    def __init__(self):
        self.word2idx = {'<PAD>': 0, '<UNK>': 1}
        self.label2idx = {'O': 0, 'B-PER': 1, 'I-PER': 2, 'B-ORG': 3, 'I-ORG': 4, 'B-LOC': 5, 'I-LOC': 6}
        self.idx2label = {v: k for k, v in self.label2idx.items()}
        
    def build_vocab(self, texts: List[str]):
        """构建词汇表"""
        word_count = Counter()
        for text in texts:
            words = list(jieba.cut(text))
            word_count.update(words)
        
        # 添加高频词到词汇表
        for word, count in word_count.most_common():
            if count >= 2:  # 只保留出现次数>=2的词
                self.word2idx[word] = len(self.word2idx)
                
        print(f"词汇表大小: {len(self.word2idx)}")
        
    def annotate_text(self, text: str) -> List[str]:
        """简单的规则标注，用于演示"""
        words = list(jieba.cut(text))
        labels = ['O'] * len(words)
        
        # 简单的人名识别（这里只是演示，实际项目中需要更复杂的标注）
        person_names = ['马云', '乔布斯', '比尔·盖茨', '保罗·艾伦', '埃隆·马斯克', '李彦宏']
        org_names = ['阿里巴巴', '苹果公司', '微软公司', '特斯拉', 'SpaceX', '百度公司']
        loc_names = ['杭州', '加利福尼亚州', '库比蒂诺', '华盛顿州', '雷德蒙德', '北京市']
        
        for i, word in enumerate(words):
            if word in person_names:
                labels[i] = 'B-PER'
            elif word in org_names:
                labels[i] = 'B-ORG'
            elif word in loc_names:
                labels[i] = 'B-LOC'
                
        return labels
    
    def create_dataset(self, texts: List[str], max_len: int = 128):
        """创建数据集"""
        # 构建词汇表
        self.build_vocab(texts)
        
        # 生成标签（实际项目中应该是人工标注的）
        all_labels = []
        for text in texts:
            labels = self.annotate_text(text)
            all_labels.append(labels)
        
        # 创建数据集
        dataset = NERDataset(texts, all_labels, self.word2idx, self.label2idx, max_len)
        return dataset
    
    def get_vocab_size(self):
        return len(self.word2idx)
    
    def get_num_labels(self):
        return len(self.label2idx)