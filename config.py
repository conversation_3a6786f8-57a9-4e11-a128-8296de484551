"""
字符级别NER模型超参数配置文件
可以通过修改这个文件来调整模型参数，无需修改训练脚本
"""

# 🔧 数据配置
DATA_CONFIG = {
    'data_file': 'char_ner_train_10k.json',  # 训练数据文件
    'batch_size': 64,                        # 批次大小 (16, 32, 64, 128)
    'max_len': 128,                          # 最大序列长度 (64, 128, 256)
    'train_ratio': 0.8,                      # 训练集比例 (0.7, 0.8, 0.9)
}

# 🧠 模型架构配置
MODEL_CONFIG = {
    'embedding_dim': 256,      # 词嵌入维度 (128, 256, 512)
    'hidden_dim': 512,         # LSTM隐藏层维度 (256, 512, 1024)
    'n_layers': 2,             # LSTM层数 (1, 2, 3, 4)
    'bidirectional': True,     # 是否使用双向LSTM
    'dropout': 0.3,            # Dropout率 (0.1, 0.3, 0.5)
}

# ⚡ 训练配置
TRAINING_CONFIG = {
    'num_epochs': 30,                    # 最大训练轮数
    'learning_rate': 0.001,              # 初始学习率 (0.0001, 0.001, 0.01)
    'weight_decay': 1e-5,                # L2正则化权重衰减
    'early_stopping_patience': 8,        # 早停耐心值
    'lr_scheduler_patience': 5,          # 学习率调度器耐心值
    'lr_scheduler_factor': 0.5,          # 学习率衰减因子
}

# 🎯 预设配置方案
PRESET_CONFIGS = {
    # 快速训练配置 (适合快速测试)
    'fast': {
        **DATA_CONFIG,
        **MODEL_CONFIG,
        **TRAINING_CONFIG,
        'batch_size': 128,
        'embedding_dim': 128,
        'hidden_dim': 256,
        'num_epochs': 15,
        'early_stopping_patience': 5,
    },
    
    # 标准配置 (当前使用的配置)
    'standard': {
        **DATA_CONFIG,
        **MODEL_CONFIG,
        **TRAINING_CONFIG,
    },
    
    # 高性能配置 (追求最佳效果)
    'high_performance': {
        **DATA_CONFIG,
        **MODEL_CONFIG,
        **TRAINING_CONFIG,
        'batch_size': 32,
        'embedding_dim': 512,
        'hidden_dim': 1024,
        'n_layers': 3,
        'learning_rate': 0.0005,
        'num_epochs': 50,
        'early_stopping_patience': 12,
    },
    
    # 防过拟合配置
    'regularized': {
        **DATA_CONFIG,
        **MODEL_CONFIG,
        **TRAINING_CONFIG,
        'dropout': 0.5,
        'weight_decay': 1e-4,
        'learning_rate': 0.0005,
        'early_stopping_patience': 5,
    }
}

def get_config(preset='standard'):
    """
    获取指定预设的配置
    
    Args:
        preset (str): 预设名称 ('fast', 'standard', 'high_performance', 'regularized')
    
    Returns:
        dict: 配置字典
    """
    if preset not in PRESET_CONFIGS:
        print(f"警告: 预设 '{preset}' 不存在，使用默认 'standard' 配置")
        preset = 'standard'
    
    config = PRESET_CONFIGS[preset].copy()
    print(f"✅ 使用 '{preset}' 配置预设")
    return config

def print_config(config):
    """打印配置信息"""
    print("\n📋 当前配置:")
    print("=" * 50)
    
    print("🔧 数据配置:")
    data_keys = ['data_file', 'batch_size', 'max_len', 'train_ratio']
    for key in data_keys:
        if key in config:
            print(f"  {key}: {config[key]}")
    
    print("\n🧠 模型配置:")
    model_keys = ['embedding_dim', 'hidden_dim', 'n_layers', 'bidirectional', 'dropout']
    for key in model_keys:
        if key in config:
            print(f"  {key}: {config[key]}")
    
    print("\n⚡ 训练配置:")
    training_keys = ['num_epochs', 'learning_rate', 'weight_decay', 'early_stopping_patience']
    for key in training_keys:
        if key in config:
            print(f"  {key}: {config[key]}")
    
    print("=" * 50)

# 超参数调优建议
TUNING_SUGGESTIONS = {
    'overfitting': {
        'description': '如果模型过拟合 (训练准确率高，验证准确率低)',
        'suggestions': [
            '增大 dropout (0.3 -> 0.5)',
            '增大 weight_decay (1e-5 -> 1e-4)',
            '减少 n_layers (3 -> 2)',
            '减小 hidden_dim (512 -> 256)',
            '减少 num_epochs',
            '增加训练数据量'
        ]
    },
    
    'underfitting': {
        'description': '如果模型欠拟合 (训练和验证准确率都低)',
        'suggestions': [
            '增大 hidden_dim (256 -> 512)',
            '增大 embedding_dim (128 -> 256)',
            '增加 n_layers (2 -> 3)',
            '减小 dropout (0.5 -> 0.3)',
            '增加 num_epochs',
            '降低 learning_rate (0.001 -> 0.0005)'
        ]
    },
    
    'slow_training': {
        'description': '如果训练速度太慢',
        'suggestions': [
            '增大 batch_size (32 -> 64)',
            '减小 max_len (128 -> 64)',
            '减小 hidden_dim (512 -> 256)',
            '减少 n_layers (3 -> 2)',
            '使用GPU训练'
        ]
    },
    
    'poor_convergence': {
        'description': '如果模型收敛缓慢或不稳定',
        'suggestions': [
            '调整 learning_rate (0.001 -> 0.0005 或 0.01)',
            '使用学习率调度器',
            '增加 batch_size',
            '检查数据质量',
            '使用梯度裁剪'
        ]
    }
}

def print_tuning_suggestions():
    """打印超参数调优建议"""
    print("\n🎯 超参数调优建议:")
    print("=" * 60)
    
    for issue, info in TUNING_SUGGESTIONS.items():
        print(f"\n📌 {info['description']}:")
        for suggestion in info['suggestions']:
            print(f"   • {suggestion}")
    
    print("=" * 60)

if __name__ == "__main__":
    # 演示配置使用
    print("🚀 字符级别NER超参数配置")
    
    # 显示所有预设
    print(f"\n📦 可用的配置预设: {list(PRESET_CONFIGS.keys())}")
    
    # 获取标准配置
    config = get_config('standard')
    print_config(config)
    
    # 显示调优建议
    print_tuning_suggestions()