import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from 标准 import nerbilstm
from data_loader import create_data_loaders
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
import json
from datetime import datetime
import os

class NERTrainer:
    """NER模型训练器"""
    
    def __init__(self, config: dict):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 训练历史记录
        self.train_history = {
            'train_loss': [],
            'val_loss': [],
            'train_acc': [],
            'val_acc': [],
            'learning_rates': []
        }
        
    def load_data(self):
        """加载数据"""
        print("=== 加载训练数据 ===")
        
        train_loader, val_loader, dataset = create_data_loaders(
            data_file=self.config['data_file'],
            batch_size=self.config['batch_size'],
            max_len=self.config['max_len'],
            train_ratio=self.config['train_ratio']
        )
        
        if train_loader is None:
            raise FileNotFoundError("数据加载失败，请先运行 generate_ner_data.py 生成数据")
        
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.dataset = dataset
        
        print(f"训练批次数: {len(train_loader)}")
        print(f"验证批次数: {len(val_loader)}")
        
    def create_model(self):
        """创建模型"""
        print("=== 创建模型 ===")
        
        self.model = nerbilstm(
            vocab_size=self.dataset.get_vocab_size(),
            embedding_dim=self.config['embedding_dim'],
            hidden_dim=self.config['hidden_dim'],
            output_dim=self.dataset.get_num_labels(),
            n_layers=self.config['n_layers'],
            bidirectional=self.config['bidirectional'],
            dropout=self.config['dropout']
        ).to(self.device)
        
        # 计算参数量
        total_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"模型参数量: {total_params:,}")
        
        # 创建优化器和损失函数
        self.criterion = nn.CrossEntropyLoss(ignore_index=self.dataset.label2idx['O'])
        self.optimizer = optim.Adam(
            self.model.parameters(), 
            lr=self.config['learning_rate'],
            weight_decay=self.config['weight_decay']
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, 
            mode='min', 
            factor=0.5, 
            patience=5, 
            verbose=True
        )
        
    def train_epoch(self, epoch: int):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        total_correct = 0
        total_tokens = 0
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{self.config["num_epochs"]}')
        
        for batch_idx, (texts, labels) in enumerate(progress_bar):
            texts, labels = texts.to(self.device), labels.to(self.device)
            
            # 清零梯度
            self.optimizer.zero_grad()
            
            # 前向传播
            outputs = self.model(texts)  # (batch_size, seq_len, num_labels)
            
            # 计算损失
            loss = self.criterion(outputs.view(-1, self.dataset.get_num_labels()), labels.view(-1))
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            # 更新参数
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            
            # 计算准确率（忽略padding）
            pred = outputs.argmax(dim=-1)
            mask = labels != self.dataset.label2idx['O']
            correct = ((pred == labels) * mask).sum().item()
            tokens = mask.sum().item()
            
            total_correct += correct
            total_tokens += tokens
            
            # 更新进度条
            current_acc = correct / tokens if tokens > 0 else 0
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{current_acc:.4f}'
            })
        
        avg_loss = total_loss / len(self.train_loader)
        avg_acc = total_correct / total_tokens if total_tokens > 0 else 0
        
        return avg_loss, avg_acc
    
    def validate(self):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        total_correct = 0
        total_tokens = 0
        
        with torch.no_grad():
            for texts, labels in tqdm(self.val_loader, desc='Validating'):
                texts, labels = texts.to(self.device), labels.to(self.device)
                
                outputs = self.model(texts)
                loss = self.criterion(outputs.view(-1, self.dataset.get_num_labels()), labels.view(-1))
                
                total_loss += loss.item()
                
                # 计算准确率
                pred = outputs.argmax(dim=-1)
                mask = labels != self.dataset.label2idx['O']
                total_correct += ((pred == labels) * mask).sum().item()
                total_tokens += mask.sum().item()
        
        avg_loss = total_loss / len(self.val_loader)
        avg_acc = total_correct / total_tokens if total_tokens > 0 else 0
        
        return avg_loss, avg_acc
    
    def train(self):
        """完整训练流程"""
        print("=== 开始训练 ===")
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(self.config['num_epochs']):
            # 训练
            train_loss, train_acc = self.train_epoch(epoch)
            
            # 验证
            val_loss, val_acc = self.validate()
            
            # 更新学习率
            self.scheduler.step(val_loss)
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # 记录历史
            self.train_history['train_loss'].append(train_loss)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['train_acc'].append(train_acc)
            self.train_history['val_acc'].append(val_acc)
            self.train_history['learning_rates'].append(current_lr)
            
            # 打印结果
            print(f'\nEpoch {epoch+1}/{self.config["num_epochs"]}:')
            print(f'  训练 - 损失: {train_loss:.4f}, 准确率: {train_acc:.4f}')
            print(f'  验证 - 损失: {val_loss:.4f}, 准确率: {val_acc:.4f}')
            print(f'  学习率: {current_lr:.6f}')
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                self.save_model('best_ner_large.pth')
                print(f'  ✅ 保存最佳模型 (验证损失: {val_loss:.4f})')
            else:
                patience_counter += 1
            
            # 早停
            if patience_counter >= self.config['early_stopping_patience']:
                print(f'\n早停触发！连续 {patience_counter} 个epoch验证损失未改善')
                break
            
            print('-' * 60)
        
        print("\n🎉 训练完成！")
        
    def save_model(self, filename: str):
        """保存模型和相关信息"""
        # 保存模型状态
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config,
            'vocab_size': self.dataset.get_vocab_size(),
            'num_labels': self.dataset.get_num_labels(),
            'word2idx': self.dataset.word2idx,
            'label2idx': self.dataset.label2idx,
            'idx2word': self.dataset.idx2word,
            'idx2label': self.dataset.idx2label
        }, filename)
        
        # 保存训练历史
        history_file = filename.replace('.pth', '_history.json')
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(self.train_history, f, indent=2)
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        if not self.train_history['train_loss']:
            print("没有训练历史数据")
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        epochs = range(1, len(self.train_history['train_loss']) + 1)
        
        # 损失曲线
        ax1.plot(epochs, self.train_history['train_loss'], 'b-', label='训练损失')
        ax1.plot(epochs, self.train_history['val_loss'], 'r-', label='验证损失')
        ax1.set_title('训练和验证损失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # 准确率曲线
        ax2.plot(epochs, self.train_history['train_acc'], 'b-', label='训练准确率')
        ax2.plot(epochs, self.train_history['val_acc'], 'r-', label='验证准确率')
        ax2.set_title('训练和验证准确率')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
        ax2.grid(True)
        
        # 学习率曲线
        ax3.plot(epochs, self.train_history['learning_rates'], 'g-')
        ax3.set_title('学习率变化')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.set_yscale('log')
        ax3.grid(True)
        
        # 损失对比
        ax4.plot(epochs, np.array(self.train_history['train_loss']) - np.array(self.train_history['val_loss']), 'purple')
        ax4.set_title('训练验证损失差异')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Train Loss - Val Loss')
        ax4.axhline(y=0, color='k', linestyle='--', alpha=0.3)
        ax4.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_curves.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("训练曲线已保存为 training_curves.png")

def main():
    """主函数"""
    # 训练配置
    config = {
        # 数据配置
        'data_file': 'ner_train_10k.json',
        'batch_size': 32,
        'max_len': 64,
        'train_ratio': 0.8,
        
        # 模型配置
        'embedding_dim': 128,
        'hidden_dim': 256,
        'n_layers': 2,
        'bidirectional': True,
        'dropout': 0.3,
        
        # 训练配置
        'num_epochs': 50,
        'learning_rate': 0.001,
        'weight_decay': 1e-5,
        'early_stopping_patience': 10,
    }
    
    print("=== BiLSTM NER 大规模数据训练 ===")
    print(f"配置: {json.dumps(config, indent=2, ensure_ascii=False)}")
    
    # 创建训练器
    trainer = NERTrainer(config)
    
    try:
        # 加载数据
        trainer.load_data()
        
        # 创建模型
        trainer.create_model()
        
        # 开始训练
        trainer.train()
        
        # 绘制训练曲线
        trainer.plot_training_curves()
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        print("请确保已经运行 generate_ner_data.py 生成训练数据")

if __name__ == "__main__":
    main()