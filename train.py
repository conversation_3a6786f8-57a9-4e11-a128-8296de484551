import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from 标准 import nerbilstm
from data_processor import NERDataProcessor
import numpy as np
from sklearn.metrics import classification_report
from tqdm import tqdm

def train_model():
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 读取数据
    with open('标注测试.txt', 'r', encoding='utf-8') as f:
        texts = [line.strip() for line in f.readlines()]
    
    print(f"数据样本数: {len(texts)}")
    
    # 数据处理
    processor = NERDataProcessor()
    dataset = processor.create_dataset(texts, max_len=64)
    
    # 数据分割
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False)
    
    # 模型参数
    vocab_size = processor.get_vocab_size()
    embedding_dim = 100
    hidden_dim = 128
    output_dim = processor.get_num_labels()
    n_layers = 2
    bidirectional = True
    dropout = 0.3
    
    # 初始化模型
    model = nerbilstm(
        vocab_size=vocab_size,
        embedding_dim=embedding_dim,
        hidden_dim=hidden_dim,
        output_dim=output_dim,
        n_layers=n_layers,
        bidirectional=bidirectional,
        dropout=dropout
    ).to(device)
    
    print(f"模型参数量: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")
    
    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss(ignore_index=0)  # 忽略padding
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)
    
    # 训练循环
    num_epochs = 50
    best_val_loss = float('inf')
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0
        
        for batch_texts, batch_labels in tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs}'):
            batch_texts, batch_labels = batch_texts.to(device), batch_labels.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(batch_texts)  # (batch_size, seq_len, num_labels)
            
            # 计算损失
            loss = criterion(outputs.view(-1, output_dim), batch_labels.view(-1))
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            
            # 计算准确率
            pred = outputs.argmax(dim=-1)
            mask = batch_labels != 0  # 非padding位置
            train_correct += ((pred == batch_labels) * mask).sum().item()
            train_total += mask.sum().item()
        
        # 验证阶段
        model.eval()
        val_loss = 0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for batch_texts, batch_labels in val_loader:
                batch_texts, batch_labels = batch_texts.to(device), batch_labels.to(device)
                
                outputs = model(batch_texts)
                loss = criterion(outputs.view(-1, output_dim), batch_labels.view(-1))
                
                val_loss += loss.item()
                
                pred = outputs.argmax(dim=-1)
                mask = batch_labels != 0
                val_correct += ((pred == batch_labels) * mask).sum().item()
                val_total += mask.sum().item()
        
        # 计算平均损失和准确率
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        train_acc = train_correct / train_total if train_total > 0 else 0
        val_acc = val_correct / val_total if val_total > 0 else 0
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  训练损失: {avg_train_loss:.4f}, 训练准确率: {train_acc:.4f}')
        print(f'  验证损失: {avg_val_loss:.4f}, 验证准确率: {val_acc:.4f}')
        print(f'  学习率: {scheduler.get_last_lr()[0]:.6f}')
        print('-' * 50)
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), 'best_ner_model.pth')
            print("保存最佳模型!")
        
        scheduler.step()
    
    print("训练完成!")
    return model, processor

def test_model(model, processor, text):
    """测试模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    # 分词
    import jieba
    words = list(jieba.cut(text))
    
    # 转换为索引
    word_ids = [processor.word2idx.get(word, processor.word2idx['<UNK>']) for word in words]
    
    # 填充
    max_len = 64
    if len(word_ids) < max_len:
        word_ids.extend([processor.word2idx['<PAD>']] * (max_len - len(word_ids)))
    else:
        word_ids = word_ids[:max_len]
    
    # 转换为tensor
    input_tensor = torch.tensor([word_ids]).to(device)
    
    with torch.no_grad():
        outputs = model(input_tensor)
        predictions = outputs.argmax(dim=-1).cpu().numpy()[0]
    
    # 解码结果
    result = []
    for i, word in enumerate(words[:len(words)]):
        if i < len(predictions):
            label = processor.idx2label[predictions[i]]
            result.append((word, label))
    
    return result

if __name__ == "__main__":
    # 训练模型
    model, processor = train_model()
    
    # 测试示例
    test_text = "马云是阿里巴巴的创始人，总部位于杭州。"
    result = test_model(model, processor, test_text)
    
    print("\n测试结果:")
    print(f"输入: {test_text}")
    print("识别结果:")
    for word, label in result:
        if label != 'O':
            print(f"  {word}: {label}")