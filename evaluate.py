import torch
from 标准 import nerbilstm
from data_processor import NERDataProcessor
from train import test_model
import jieba

def load_model(model_path, vocab_size, embedding_dim=100, hidden_dim=128, output_dim=7, n_layers=2):
    """加载训练好的模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = nerbilstm(
        vocab_size=vocab_size,
        embedding_dim=embedding_dim,
        hidden_dim=hidden_dim,
        output_dim=output_dim,
        n_layers=n_layers,
        bidirectional=True,
        dropout=0.3
    )
    
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.to(device)
    model.eval()
    
    return model

def evaluate_on_test_data():
    """在测试数据上评估模型"""
    # 重新创建processor（实际应用中应该保存processor）
    processor = NERDataProcessor()
    
    # 读取数据重新构建词汇表
    with open('标注测试.txt', 'r', encoding='utf-8') as f:
        texts = [line.strip() for line in f.readlines()]
    
    processor.build_vocab(texts)
    
    # 加载模型
    try:
        model = load_model('best_ner_model.pth', processor.get_vocab_size())
        print("模型加载成功!")
    except FileNotFoundError:
        print("模型文件不存在，请先训练模型!")
        return
    
    # 测试每个句子
    print("\n=== NER识别结果 ===")
    for i, text in enumerate(texts, 1):
        print(f"\n句子 {i}: {text}")
        result = test_model(model, processor, text)
        
        entities = []
        for word, label in result:
            if label != 'O':
                entity_type = label.split('-')[1] if '-' in label else label
                entities.append(f"{word}({entity_type})")
        
        if entities:
            print(f"识别的实体: {', '.join(entities)}")
        else:
            print("未识别到实体")

def interactive_test():
    """交互式测试"""
    # 重新创建processor
    processor = NERDataProcessor()
    
    # 读取数据重新构建词汇表
    with open('标注测试.txt', 'r', encoding='utf-8') as f:
        texts = [line.strip() for line in f.readlines()]
    
    processor.build_vocab(texts)
    
    # 加载模型
    try:
        model = load_model('best_ner_model.pth', processor.get_vocab_size())
        print("模型加载成功!")
    except FileNotFoundError:
        print("模型文件不存在，请先训练模型!")
        return
    
    print("\n=== 交互式NER测试 ===")
    print("输入文本进行实体识别（输入'quit'退出）:")
    
    while True:
        text = input("\n请输入文本: ").strip()
        if text.lower() == 'quit':
            break
        
        if not text:
            continue
        
        result = test_model(model, processor, text)
        
        print(f"\n原文: {text}")
        print("分词结果:")
        for word, label in result:
            if label == 'O':
                print(f"  {word}")
            else:
                entity_type = {'PER': '人名', 'ORG': '机构', 'LOC': '地点'}.get(label.split('-')[1], label)
                print(f"  {word} -> {entity_type}")
        
        # 提取实体
        entities = {}
        for word, label in result:
            if label != 'O':
                entity_type = label.split('-')[1] if '-' in label else label
                if entity_type not in entities:
                    entities[entity_type] = []
                entities[entity_type].append(word)
        
        if entities:
            print("\n识别的实体:")
            type_map = {'PER': '人名', 'ORG': '机构', 'LOC': '地点'}
            for ent_type, words in entities.items():
                type_name = type_map.get(ent_type, ent_type)
                print(f"  {type_name}: {', '.join(words)}")
        else:
            print("\n未识别到实体")

if __name__ == "__main__":
    print("选择评估模式:")
    print("1. 在测试数据上评估")
    print("2. 交互式测试")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == '1':
        evaluate_on_test_data()
    elif choice == '2':
        interactive_test()
    else:
        print("无效选择!")