import random
import json
from typing import List, Tuple, Dict
import jieba

class NERDataGenerator:
    def __init__(self):
        # 人名数据库
        self.person_names = [
            # 中国名人
            "马云", "马化腾", "李彦宏", "雷军", "刘强东", "张一鸣", "王兴", "程维",
            "黄峥", "丁磊", "张朝阳", "李开复", "周鸿祎", "俞敏洪", "王石", "任正非",
            "宗庆后", "曹德旺", "刘永好", "许家印", "王健林", "潘石屹", "冯仑", "柳传志",
            # 国际名人  
            "乔布斯", "比尔·盖茨", "埃隆·马斯克", "扎克伯格", "杰夫·贝佐斯", "拉里·佩奇",
            "蒂姆·库克", "苏珊·沃西基", "萨提亚·纳德拉", "马克·库班", "理查德·布兰森",
            # 常见中文姓名
            "张伟", "王芳", "李娜", "刘洋", "陈静", "杨明", "赵丽", "黄强", "周敏", "吴涛",
            "徐磊", "孙超", "朱艳", "高峰", "林雪", "何建", "郭勇", "罗萍", "梁军", "谢红",
            "唐亮", "韩雨", "冯云", "曾华", "彭飞", "蒋丽", "蔡明", "田野", "董鹏", "于洋"
        ]
        
        # 机构名称数据库
        self.organizations = [
            # 科技公司
            "阿里巴巴", "腾讯", "百度", "字节跳动", "美团", "滴滴出行", "小米科技", 
            "华为技术", "京东集团", "网易", "新浪", "搜狐", "360集团", "拼多多",
            "苹果公司", "微软公司", "谷歌", "亚马逊", "Meta", "特斯拉", "SpaceX",
            "英伟达", "英特尔", "AMD", "高通", "联想集团", "戴尔科技",
            # 金融机构
            "中国银行", "工商银行", "建设银行", "农业银行", "招商银行", "平安银行",
            "中信银行", "民生银行", "光大银行", "华夏银行", "浦发银行", "兴业银行",
            "支付宝", "微信支付", "蚂蚁集团", "陆金所", "宜信", "拍拍贷",
            # 教育机构
            "清华大学", "北京大学", "复旦大学", "上海交通大学", "浙江大学", "南京大学",
            "中山大学", "华中科技大学", "西安交通大学", "哈尔滨工业大学", "北京理工大学",
            "新东方", "好未来", "VIPKID", "猿辅导", "作业帮", "学而思",
            # 传统企业
            "中国石油", "中国石化", "国家电网", "中国移动", "中国联通", "中国电信",
            "万科集团", "恒大集团", "碧桂园", "保利地产", "中海地产", "华润置地"
        ]
        
        # 地点数据库
        self.locations = [
            # 城市
            "北京", "上海", "深圳", "广州", "杭州", "南京", "苏州", "成都", "重庆", "天津",
            "武汉", "西安", "青岛", "大连", "宁波", "厦门", "福州", "长沙", "郑州", "济南",
            "合肥", "昆明", "南昌", "贵阳", "南宁", "海口", "拉萨", "银川", "西宁", "乌鲁木齐",
            # 省份/直辖市
            "北京市", "上海市", "天津市", "重庆市", "河北省", "山西省", "辽宁省", "吉林省",
            "黑龙江省", "江苏省", "浙江省", "安徽省", "福建省", "江西省", "山东省", "河南省",
            "湖北省", "湖南省", "广东省", "海南省", "四川省", "贵州省", "云南省", "陕西省",
            "甘肃省", "青海省", "台湾省", "广西壮族自治区", "内蒙古自治区", "西藏自治区",
            "宁夏回族自治区", "新疆维吾尔自治区", "香港特别行政区", "澳门特别行政区",
            # 国际地点
            "纽约", "洛杉矶", "芝加哥", "华盛顿", "旧金山", "西雅图", "波士顿", "费城",
            "伦敦", "巴黎", "柏林", "罗马", "马德里", "阿姆斯特丹", "苏黎世", "维也纳",
            "东京", "大阪", "首尔", "新加坡", "曼谷", "雅加达", "孟买", "新德里",
            "悉尼", "墨尔本", "多伦多", "温哥华", "圣保罗", "布宜诺斯艾利斯"
        ]
        
        # 句子模板
        self.templates = [
            # 人物介绍类
            "{person}是{org}的创始人。",
            "{person}在{org}担任CEO。",
            "{person}曾在{org}工作多年。",
            "{person}是{org}的董事长。",
            "{person}创立了{org}公司。",
            "{person}目前担任{org}的总裁。",
            "{person}是{org}的联合创始人。",
            "{person}在{org}负责技术开发。",
            
            # 公司地点类
            "{org}的总部位于{location}。",
            "{org}在{location}设立了分公司。",
            "{org}计划在{location}建设新园区。",
            "{org}的研发中心设在{location}。",
            "{org}在{location}开设了办事处。",
            "{org}将在{location}投资建厂。",
            "{org}的亚洲总部位于{location}。",
            "{org}在{location}举办了发布会。",
            
            # 人物地点类
            "{person}出生于{location}。",
            "{person}在{location}长大。",
            "{person}目前居住在{location}。",
            "{person}经常往返于{location}。",
            "{person}在{location}接受了教育。",
            "{person}计划搬到{location}。",
            "{person}在{location}购买了房产。",
            "{person}在{location}进行商务考察。",
            
            # 复合类
            "{person}在{location}创立了{org}。",
            "{person}将{org}的总部迁至{location}。",
            "{person}在{location}宣布{org}的新战略。",
            "{person}代表{org}在{location}签署协议。",
            "{person}在{location}为{org}开设新店。",
            "{person}和{org}在{location}举行会议。",
            "{person}决定让{org}在{location}上市。",
            "{person}带领{org}团队访问{location}。",
            
            # 新闻类
            "据报道，{person}离开了{org}。",
            "消息称，{person}加入了{org}。",
            "业内人士透露，{person}正在考虑收购{org}。",
            "有传言称，{person}将投资{org}。",
            "分析师认为，{person}的决策影响了{org}。",
            "媒体报道，{person}在{location}会见了{org}高管。",
            "权威消息显示，{person}计划在{location}扩张{org}业务。",
            "最新消息，{person}在{location}与{org}达成合作。",
            
            # 事件类
            "{person}参加了在{location}举行的会议。",
            "{person}在{location}发表了重要演讲。",
            "{person}访问了位于{location}的{org}。",
            "{person}在{location}与{org}签署了合同。",
            "{person}宣布{org}将进军{location}市场。",
            "{person}在{location}启动了{org}的新项目。",
            "{person}和{org}在{location}召开发布会。",
            "{person}决定将{org}的业务拓展到{location}。"
        ]
    
    def generate_sentence(self) -> Tuple[str, List[Tuple[str, str]]]:
        """生成一个句子和对应的实体标注"""
        template = random.choice(self.templates)
        entities = []
        
        # 替换模板中的占位符
        sentence = template
        
        # 替换人名
        if "{person}" in sentence:
            person = random.choice(self.person_names)
            sentence = sentence.replace("{person}", person, 1)
            entities.append((person, "PER"))
        
        # 替换机构名
        if "{org}" in sentence:
            org = random.choice(self.organizations)
            sentence = sentence.replace("{org}", org, 1)
            entities.append((org, "ORG"))
        
        # 替换地点
        if "{location}" in sentence:
            location = random.choice(self.locations)
            sentence = sentence.replace("{location}", location, 1)
            entities.append((location, "LOC"))
        
        return sentence, entities
    
    def sentence_to_bio(self, sentence: str, entities: List[Tuple[str, str]]) -> List[Tuple[str, str]]:
        """将句子转换为BIO标注格式"""
        # 分词
        words = list(jieba.cut(sentence))
        labels = ['O'] * len(words)
        
        # 为每个实体标注BIO标签
        for entity_text, entity_type in entities:
            entity_words = list(jieba.cut(entity_text))
            
            # 在分词结果中找到实体的位置
            for i in range(len(words) - len(entity_words) + 1):
                if words[i:i+len(entity_words)] == entity_words:
                    # 标注B-和I-标签
                    labels[i] = f'B-{entity_type}'
                    for j in range(1, len(entity_words)):
                        if i + j < len(labels):
                            labels[i + j] = f'I-{entity_type}'
                    break
        
        return list(zip(words, labels))
    
    def generate_dataset(self, num_samples: int = 10000) -> List[Dict]:
        """生成指定数量的NER数据集"""
        dataset = []
        
        print(f"开始生成 {num_samples} 条NER训练数据...")
        
        for i in range(num_samples):
            if (i + 1) % 1000 == 0:
                print(f"已生成 {i + 1} 条数据...")
            
            # 生成句子和实体
            sentence, entities = self.generate_sentence()
            
            # 转换为BIO格式
            bio_data = self.sentence_to_bio(sentence, entities)
            
            # 创建数据项
            data_item = {
                "id": i + 1,
                "sentence": sentence,
                "entities": entities,
                "tokens": [token for token, label in bio_data],
                "labels": [label for token, label in bio_data]
            }
            
            dataset.append(data_item)
        
        print(f"数据生成完成！共生成 {len(dataset)} 条数据")
        return dataset
    
    def save_dataset(self, dataset: List[Dict], filename: str = "ner_train_data.json"):
        """保存数据集到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)
        print(f"数据集已保存到 {filename}")
    
    def save_bio_format(self, dataset: List[Dict], filename: str = "ner_train_bio.txt"):
        """保存为BIO格式的文本文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            for item in dataset:
                tokens = item['tokens']
                labels = item['labels']
                
                for token, label in zip(tokens, labels):
                    f.write(f"{token}\t{label}\n")
                f.write("\n")  # 句子之间空行分隔
        
        print(f"BIO格式数据已保存到 {filename}")
    
    def print_sample_data(self, dataset: List[Dict], num_samples: int = 5):
        """打印一些样本数据"""
        print(f"\n=== 样本数据预览 (前{num_samples}条) ===")
        
        for i in range(min(num_samples, len(dataset))):
            item = dataset[i]
            print(f"\n样本 {i+1}:")
            print(f"句子: {item['sentence']}")
            print(f"实体: {item['entities']}")
            print("BIO标注:")
            for token, label in zip(item['tokens'], item['labels']):
                if label != 'O':
                    print(f"  {token} -> {label}")
                else:
                    print(f"  {token}")
    
    def get_statistics(self, dataset: List[Dict]) -> Dict:
        """统计数据集信息"""
        total_sentences = len(dataset)
        total_tokens = sum(len(item['tokens']) for item in dataset)
        
        entity_counts = {'PER': 0, 'ORG': 0, 'LOC': 0}
        label_counts = {'O': 0, 'B-PER': 0, 'I-PER': 0, 'B-ORG': 0, 'I-ORG': 0, 'B-LOC': 0, 'I-LOC': 0}
        
        for item in dataset:
            # 统计实体
            for _, entity_type in item['entities']:
                entity_counts[entity_type] += 1
            
            # 统计标签
            for label in item['labels']:
                label_counts[label] += 1
        
        stats = {
            'total_sentences': total_sentences,
            'total_tokens': total_tokens,
            'avg_tokens_per_sentence': total_tokens / total_sentences,
            'entity_counts': entity_counts,
            'label_counts': label_counts
        }
        
        return stats
    
    def print_statistics(self, dataset: List[Dict]):
        """打印数据集统计信息"""
        stats = self.get_statistics(dataset)
        
        print(f"\n=== 数据集统计信息 ===")
        print(f"总句子数: {stats['total_sentences']}")
        print(f"总词数: {stats['total_tokens']}")
        print(f"平均每句词数: {stats['avg_tokens_per_sentence']:.2f}")
        
        print(f"\n实体统计:")
        for entity_type, count in stats['entity_counts'].items():
            type_name = {'PER': '人名', 'ORG': '机构', 'LOC': '地点'}[entity_type]
            print(f"  {type_name}({entity_type}): {count}")
        
        print(f"\n标签统计:")
        for label, count in stats['label_counts'].items():
            percentage = count / stats['total_tokens'] * 100
            print(f"  {label}: {count} ({percentage:.2f}%)")

def main():
    """主函数"""
    generator = NERDataGenerator()
    
    # 生成10000条数据
    dataset = generator.generate_dataset(10000)
    
    # 打印样本数据
    generator.print_sample_data(dataset, 5)
    
    # 打印统计信息
    generator.print_statistics(dataset)
    
    # 保存数据
    generator.save_dataset(dataset, "ner_train_10k.json")
    generator.save_bio_format(dataset, "ner_train_10k_bio.txt")
    
    print("\n✅ 数据生成完成！")
    print("📁 生成的文件:")
    print("  - ner_train_10k.json (JSON格式)")
    print("  - ner_train_10k_bio.txt (BIO格式)")

if __name__ == "__main__":
    main()