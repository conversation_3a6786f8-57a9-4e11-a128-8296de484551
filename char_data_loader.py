import json
import torch
from torch.utils.data import Dataset, DataLoader
from collections import Counter
from typing import List, Dict, Tuple
import random

class CharNERDataset(Dataset):
    """用于处理字符级别NER数据的Dataset类"""
    
    def __init__(self, data_file: str, max_len: int = 128, train_ratio: float = 0.8):
        self.max_len = max_len
        self.data = self.load_data(data_file)
        
        # 构建字符表和标签表
        self.build_vocabularies()
        
        # 分割训练集和验证集
        self.split_data(train_ratio)
        
    def load_data(self, data_file: str) -> List[Dict]:
        """加载数据文件"""
        print(f"正在加载字符级别数据文件: {data_file}")
        
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载 {len(data)} 条字符级别数据")
            return data
        except FileNotFoundError:
            print(f"数据文件 {data_file} 不存在，请先运行 generate_char_ner_data.py 生成数据")
            return []
    
    def build_vocabularies(self):
        """构建字符表和标签表"""
        print("正在构建字符表...")
        
        # 统计字符频率
        char_counter = Counter()
        label_counter = Counter()
        
        for item in self.data:
            char_counter.update(item['chars'])
            label_counter.update(item['labels'])
        
        # 构建字符表（保留所有字符，因为中文字符数量有限）
        self.char2idx = {'<PAD>': 0, '<UNK>': 1}
        for char, count in char_counter.most_common():
            if count >= 1:  # 保留所有出现的字符
                self.char2idx[char] = len(self.char2idx)
        
        self.idx2char = {idx: char for char, idx in self.char2idx.items()}
        
        # 构建标签表
        self.label2idx = {}
        for label in sorted(label_counter.keys()):
            self.label2idx[label] = len(self.label2idx)
        
        self.idx2label = {idx: label for label, idx in self.label2idx.items()}
        
        print(f"字符表大小: {len(self.char2idx)}")
        print(f"标签数量: {len(self.label2idx)}")
        print(f"标签: {list(self.label2idx.keys())}")
    
    def split_data(self, train_ratio: float):
        """分割训练集和验证集"""
        random.shuffle(self.data)
        
        split_idx = int(len(self.data) * train_ratio)
        self.train_data = self.data[:split_idx]
        self.val_data = self.data[split_idx:]
        
        print(f"训练集大小: {len(self.train_data)}")
        print(f"验证集大小: {len(self.val_data)}")
    
    def encode_item(self, item: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """将数据项编码为张量"""
        chars = item['chars']
        labels = item['labels']
        
        # 转换为索引
        char_ids = [self.char2idx.get(char, self.char2idx['<UNK>']) for char in chars]
        label_ids = [self.label2idx[label] for label in labels]
        
        # 填充或截断
        if len(char_ids) < self.max_len:
            # 填充
            pad_len = self.max_len - len(char_ids)
            char_ids.extend([self.char2idx['<PAD>']] * pad_len)
            label_ids.extend([self.label2idx['O']] * pad_len)
        else:
            # 截断
            char_ids = char_ids[:self.max_len]
            label_ids = label_ids[:self.max_len]
        
        return torch.tensor(char_ids), torch.tensor(label_ids)
    
    def get_train_dataset(self):
        """获取训练数据集"""
        return CharNERSubDataset(self.train_data, self)
    
    def get_val_dataset(self):
        """获取验证数据集"""
        return CharNERSubDataset(self.val_data, self)
    
    def get_vocab_size(self):
        return len(self.char2idx)
    
    def get_num_labels(self):
        return len(self.label2idx)

class CharNERSubDataset(Dataset):
    """训练/验证子数据集"""
    
    def __init__(self, data: List[Dict], parent_dataset):
        self.data = data
        self.parent = parent_dataset
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.parent.encode_item(self.data[idx])

def create_char_data_loaders(data_file: str, batch_size: int = 32, max_len: int = 128, 
                            train_ratio: float = 0.8) -> Tuple[DataLoader, DataLoader, CharNERDataset]:
    """创建字符级别数据加载器"""
    
    # 创建数据集
    dataset = CharNERDataset(data_file, max_len, train_ratio)
    
    if not dataset.data:
        return None, None, None
    
    # 创建训练和验证数据集
    train_dataset = dataset.get_train_dataset()
    val_dataset = dataset.get_val_dataset()
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        num_workers=0  # Windows兼容性
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=batch_size, 
        shuffle=False,
        num_workers=0
    )
    
    return train_loader, val_loader, dataset

def test_char_data_loader():
    """测试字符级别数据加载器"""
    print("=== 测试字符级别数据加载器 ===")
    
    # 创建数据加载器
    train_loader, val_loader, dataset = create_char_data_loaders(
        "char_ner_train_10k.json", 
        batch_size=8, 
        max_len=64
    )
    
    if train_loader is None:
        print("数据加载失败，请先生成字符级别数据")
        return
    
    print(f"\n数据加载器创建成功!")
    print(f"字符表大小: {dataset.get_vocab_size()}")
    print(f"标签数量: {dataset.get_num_labels()}")
    
    # 测试一个batch
    print(f"\n测试训练数据加载...")
    for batch_idx, (texts, labels) in enumerate(train_loader):
        print(f"Batch {batch_idx + 1}:")
        print(f"  文本张量形状: {texts.shape}")  # (batch_size, seq_len)
        print(f"  标签张量形状: {labels.shape}")  # (batch_size, seq_len)
        print(f"  文本样例: {texts[0][:10].tolist()}")  # 前10个字符的ID
        print(f"  标签样例: {labels[0][:10].tolist()}")  # 前10个标签的ID
        break  # 只测试第一个batch
    
    # 解码示例
    print(f"\n解码示例:")
    sample_text = texts[0]
    sample_labels = labels[0]
    
    decoded_chars = [dataset.idx2char.get(idx.item(), '<UNK>') for idx in sample_text[:20]]
    decoded_labels = [dataset.idx2label.get(idx.item(), 'O') for idx in sample_labels[:20]]
    
    print("字符 -> 标签:")
    for char, label in zip(decoded_chars, decoded_labels):
        if char != '<PAD>':
            print(f"  '{char}' -> {label}")

if __name__ == "__main__":
    test_char_data_loader()