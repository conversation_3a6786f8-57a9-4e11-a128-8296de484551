import json
import torch
from torch.utils.data import Dataset, DataLoader
from collections import Counter
from typing import List, Dict, Tuple
import random

class LargeNERDataset(Dataset):
    """用于处理大规模NER数据的Dataset类"""
    
    def __init__(self, data_file: str, max_len: int = 128, train_ratio: float = 0.8):
        self.max_len = max_len
        self.data = self.load_data(data_file)
        
        # 构建词汇表和标签表
        self.build_vocabularies()
        
        # 分割训练集和验证集
        self.split_data(train_ratio)
        
    def load_data(self, data_file: str) -> List[Dict]:
        """加载数据文件"""
        print(f"正在加载数据文件: {data_file}")
        
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载 {len(data)} 条数据")
            return data
        except FileNotFoundError:
            print(f"数据文件 {data_file} 不存在，请先运行 generate_ner_data.py 生成数据")
            return []
    
    def build_vocabularies(self):
        """构建词汇表和标签表"""
        print("正在构建词汇表...")
        
        # 统计词频
        word_counter = Counter()
        label_counter = Counter()
        
        for item in self.data:
            word_counter.update(item['tokens'])
            label_counter.update(item['labels'])
        
        # 构建词汇表（保留高频词）
        self.word2idx = {'<PAD>': 0, '<UNK>': 1}
        for word, count in word_counter.most_common():
            if count >= 2:  # 只保留出现次数>=2的词
                self.word2idx[word] = len(self.word2idx)
        
        self.idx2word = {idx: word for word, idx in self.word2idx.items()}
        
        # 构建标签表
        self.label2idx = {}
        for label in sorted(label_counter.keys()):
            self.label2idx[label] = len(self.label2idx)
        
        self.idx2label = {idx: label for label, idx in self.label2idx.items()}
        
        print(f"词汇表大小: {len(self.word2idx)}")
        print(f"标签数量: {len(self.label2idx)}")
        print(f"标签: {list(self.label2idx.keys())}")
    
    def split_data(self, train_ratio: float):
        """分割训练集和验证集"""
        random.shuffle(self.data)
        
        split_idx = int(len(self.data) * train_ratio)
        self.train_data = self.data[:split_idx]
        self.val_data = self.data[split_idx:]
        
        print(f"训练集大小: {len(self.train_data)}")
        print(f"验证集大小: {len(self.val_data)}")
    
    def encode_item(self, item: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """将数据项编码为张量"""
        tokens = item['tokens']
        labels = item['labels']
        
        # 转换为索引
        token_ids = [self.word2idx.get(token, self.word2idx['<UNK>']) for token in tokens]
        label_ids = [self.label2idx[label] for label in labels]
        
        # 填充或截断
        if len(token_ids) < self.max_len:
            # 填充
            pad_len = self.max_len - len(token_ids)
            token_ids.extend([self.word2idx['<PAD>']] * pad_len)
            label_ids.extend([self.label2idx['O']] * pad_len)
        else:
            # 截断
            token_ids = token_ids[:self.max_len]
            label_ids = label_ids[:self.max_len]
        
        return torch.tensor(token_ids), torch.tensor(label_ids)
    
    def get_train_dataset(self):
        """获取训练数据集"""
        return NERSubDataset(self.train_data, self)
    
    def get_val_dataset(self):
        """获取验证数据集"""
        return NERSubDataset(self.val_data, self)
    
    def get_vocab_size(self):
        return len(self.word2idx)
    
    def get_num_labels(self):
        return len(self.label2idx)

class NERSubDataset(Dataset):
    """训练/验证子数据集"""
    
    def __init__(self, data: List[Dict], parent_dataset):
        self.data = data
        self.parent = parent_dataset
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.parent.encode_item(self.data[idx])

def create_data_loaders(data_file: str, batch_size: int = 32, max_len: int = 128, 
                       train_ratio: float = 0.8) -> Tuple[DataLoader, DataLoader, LargeNERDataset]:
    """创建数据加载器"""
    
    # 创建数据集
    dataset = LargeNERDataset(data_file, max_len, train_ratio)
    
    if not dataset.data:
        return None, None, None
    
    # 创建训练和验证数据集
    train_dataset = dataset.get_train_dataset()
    val_dataset = dataset.get_val_dataset()
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        num_workers=0  # Windows兼容性
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=batch_size, 
        shuffle=False,
        num_workers=0
    )
    
    return train_loader, val_loader, dataset

def test_data_loader():
    """测试数据加载器"""
    print("=== 测试数据加载器 ===")
    
    # 创建数据加载器
    train_loader, val_loader, dataset = create_data_loaders(
        "ner_train_10k.json", 
        batch_size=8, 
        max_len=64
    )
    
    if train_loader is None:
        print("数据加载失败，请先生成数据")
        return
    
    print(f"\n数据加载器创建成功!")
    print(f"词汇表大小: {dataset.get_vocab_size()}")
    print(f"标签数量: {dataset.get_num_labels()}")
    
    # 测试一个batch
    print(f"\n测试训练数据加载...")
    for batch_idx, (texts, labels) in enumerate(train_loader):
        print(f"Batch {batch_idx + 1}:")
        print(f"  文本张量形状: {texts.shape}")  # (batch_size, seq_len)
        print(f"  标签张量形状: {labels.shape}")  # (batch_size, seq_len)
        print(f"  文本样例: {texts[0][:10].tolist()}")  # 前10个token的ID
        print(f"  标签样例: {labels[0][:10].tolist()}")  # 前10个标签的ID
        break  # 只测试第一个batch
    
    # 解码示例
    print(f"\n解码示例:")
    sample_text = texts[0]
    sample_labels = labels[0]
    
    decoded_tokens = [dataset.idx2word.get(idx.item(), '<UNK>') for idx in sample_text[:20]]
    decoded_labels = [dataset.idx2label.get(idx.item(), 'O') for idx in sample_labels[:20]]
    
    print("Token -> Label:")
    for token, label in zip(decoded_tokens, decoded_labels):
        if token != '<PAD>':
            print(f"  {token} -> {label}")

if __name__ == "__main__":
    test_data_loader()