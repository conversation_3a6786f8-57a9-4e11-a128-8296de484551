import torch
from 标准 import nerbilstm
import json
from typing import List, Tuple, Dict
import numpy as np
from collections import defaultdict

class CharNERTester:
    """字符级别NER模型测试器"""
    
    def __init__(self, model_path: str):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.load_model(model_path)
    
    def load_model(self, model_path: str):
        """加载训练好的字符级别模型"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # 加载模型配置
            self.config = checkpoint['config']
            self.char2idx = checkpoint['char2idx']
            self.label2idx = checkpoint['label2idx']
            self.idx2char = checkpoint['idx2char']
            self.idx2label = checkpoint['idx2label']
            
            # 创建模型
            self.model = nerbilstm(
                vocab_size=checkpoint['vocab_size'],
                embedding_dim=self.config['embedding_dim'],
                hidden_dim=self.config['hidden_dim'],
                output_dim=checkpoint['num_labels'],
                n_layers=self.config['n_layers'],
                bidirectional=self.config['bidirectional'],
                dropout=self.config['dropout']
            )
            
            # 加载模型参数
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            print(f"✅ 字符级别模型加载成功！")
            print(f"📊 字符表大小: {checkpoint['vocab_size']}")
            print(f"🏷️ 标签数量: {checkpoint['num_labels']}")
            print(f"🏷️ 标签类型: {list(self.label2idx.keys())}")
            
        except FileNotFoundError:
            print(f"❌ 模型文件 {model_path} 不存在，请先训练模型")
            raise
    
    def predict(self, text: str) -> List[Tuple[str, str]]:
        """对输入文本进行字符级别NER预测"""
        # 转换为字符列表
        chars = list(text)
        
        # 转换为索引
        char_ids = [self.char2idx.get(char, self.char2idx['<UNK>']) for char in chars]
        
        # 填充到最大长度
        max_len = self.config['max_len']
        original_len = len(char_ids)
        
        if len(char_ids) < max_len:
            char_ids.extend([self.char2idx['<PAD>']] * (max_len - len(char_ids)))
        else:
            char_ids = char_ids[:max_len]
        
        # 转换为tensor
        input_tensor = torch.tensor([char_ids]).to(self.device)
        
        with torch.no_grad():
            outputs = self.model(input_tensor)
            predictions = outputs.argmax(dim=-1).cpu().numpy()[0]
        
        # 解码结果（只返回原始长度的结果）
        result = []
        for i, char in enumerate(chars):
            if i < len(predictions) and i < max_len:
                label = self.idx2label[predictions[i]]
                result.append((char, label))
        
        return result
    
    def extract_entities(self, char_label_pairs: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        """从字符标签对中提取实体"""
        entities = {'PER': [], 'ORG': [], 'LOC': []}
        current_entity = ""
        current_type = None
        
        for char, label in char_label_pairs:
            if label.startswith('B-'):
                # 开始新实体
                if current_entity and current_type:
                    entities[current_type].append(current_entity)
                current_entity = char
                current_type = label[2:]  # 去掉'B-'前缀
            elif label.startswith('I-') and current_type == label[2:]:
                # 继续当前实体
                current_entity += char
            else:
                # 结束当前实体
                if current_entity and current_type:
                    entities[current_type].append(current_entity)
                current_entity = ""
                current_type = None
        
        # 处理最后一个实体
        if current_entity and current_type:
            entities[current_type].append(current_entity)
        
        return entities
    
    def test_single_sentence(self, sentence: str):
        """测试单个句子"""
        print(f"\n🔍 测试句子: {sentence}")
        
        # 预测
        char_label_pairs = self.predict(sentence)
        
        # 提取实体
        entities = self.extract_entities(char_label_pairs)
        
        # 显示字符级别标注
        print(f"\n📝 字符级别标注:")
        for i, (char, label) in enumerate(char_label_pairs):
            if label != 'O':
                print(f"  第{i+1:2d}字: '{char}' -> {label}")
            else:
                print(f"  第{i+1:2d}字: '{char}' -> O")
        
        # 显示提取的实体
        type_map = {'PER': '👤人名', 'ORG': '🏢机构', 'LOC': '📍地点'}
        print(f"\n🎯 识别的实体:")
        
        all_entities = []
        for entity_type, entity_list in entities.items():
            if entity_list:
                type_name = type_map[entity_type]
                for entity in entity_list:
                    all_entities.append(f"{entity}({type_name})")
                print(f"  {type_name}: {', '.join(entity_list)}")
        
        if not all_entities:
            print("  ❌ 未识别到任何实体")
        
        # 统计O标签比例
        o_count = sum(1 for _, label in char_label_pairs if label == 'O')
        total = len(char_label_pairs)
        o_ratio = o_count / total if total > 0 else 0
        print(f"\n📊 'O'标签比例: {o_count}/{total} = {o_ratio:.2%}")
        
        return entities, char_label_pairs
    
    def test_multiple_sentences(self):
        """测试多个句子"""
        test_sentences = [
            "马云是阿里巴巴的创始人，总部位于杭州。",
            "李彦宏在百度公司担任首席执行官。",
            "华为技术有限公司的创始人是任正非。",
            "小米科技的雷军出生于湖北仙桃。",
            "腾讯公司位于深圳，马化腾是其创始人。",
            "字节跳动的张一鸣来自福建龙岩。",
            "王兴创立了美团点评，总部在北京。",
            "滴滴出行的程维毕业于北京化工大学。",
            "拼多多的黄峥曾在上海工作。",
            "网易公司的丁磊是浙江宁波人。"
        ]
        
        print("=" * 60)
        print("🧪 多句子测试开始")
        print("=" * 60)
        
        all_stats = defaultdict(int)
        total_chars = 0
        total_entities = {'PER': 0, 'ORG': 0, 'LOC': 0}
        
        for i, sentence in enumerate(test_sentences, 1):
            print(f"\n【测试 {i}/{len(test_sentences)}】")
            entities, char_pairs = self.test_single_sentence(sentence)
            
            # 统计
            total_chars += len(char_pairs)
            for entity_type, entity_list in entities.items():
                total_entities[entity_type] += len(entity_list)
            
            for _, label in char_pairs:
                all_stats[label] += 1
        
        # 总体统计
        print("\n" + "=" * 60)
        print("📈 总体统计结果")
        print("=" * 60)
        
        print(f"🔢 总字符数: {total_chars}")
        print(f"🎯 总实体数: {sum(total_entities.values())}")
        
        for entity_type, count in total_entities.items():
            type_map = {'PER': '👤人名', 'ORG': '🏢机构', 'LOC': '📍地点'}
            print(f"  {type_map[entity_type]}: {count}")
        
        print(f"\n🏷️ 标签分布:")
        for label, count in sorted(all_stats.items()):
            ratio = count / total_chars * 100
            print(f"  {label}: {count} ({ratio:.1f}%)")
        
        # 检查O标签比例
        o_ratio = all_stats['O'] / total_chars * 100
        print(f"\n✅ 'O'标签比例: {o_ratio:.1f}% (正常应该在40-70%)")
        
        return all_stats, total_entities
    
    def test_label_distribution(self):
        """测试标签分布是否合理"""
        print("\n" + "=" * 60)
        print("🔍 标签分布分析")
        print("=" * 60)
        
        # 测试一些包含大量O标签的句子
        o_heavy_sentences = [
            "今天天气很好。",
            "这是一个测试句子。",
            "我喜欢吃苹果和香蕉。",
            "明天我要去图书馆学习。"
        ]
        
        print("\n📝 测试O标签占比较高的句子:")
        
        for sentence in o_heavy_sentences:
            print(f"\n句子: {sentence}")
            char_pairs = self.predict(sentence)
            
            o_count = sum(1 for _, label in char_pairs if label == 'O')
            total = len(char_pairs)
            o_ratio = o_count / total * 100
            
            print(f"标注结果: {[(char, label) for char, label in char_pairs]}")
            print(f"'O'标签比例: {o_count}/{total} = {o_ratio:.1f}%")
    
    def interactive_test(self):
        """交互式测试"""
        print("\n" + "=" * 60)
        print("🎮 交互式字符级别NER测试")
        print("=" * 60)
        print("💡 输入文本进行实体识别（输入'quit'退出）")
        
        while True:
            text = input("\n请输入文本: ").strip()
            if text.lower() == 'quit':
                print("👋 测试结束，再见！")
                break
            
            if not text:
                continue
            
            self.test_single_sentence(text)

def main():
    """主函数"""
    print("🚀 字符级别NER模型测试工具")
    print("=" * 60)
    
    # 加载模型
    try:
        tester = CharNERTester('model/best_char_ner.pth')
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    while True:
        print("\n🔧 选择测试模式:")
        print("1. 🧪 多句子批量测试")
        print("2. 🔍 标签分布分析")
        print("3. 🎮 交互式测试")
        print("4. 🚪 退出")
        
        choice = input("\n请选择 (1/2/3/4): ").strip()
        
        if choice == '1':
            tester.test_multiple_sentences()
        elif choice == '2':
            tester.test_label_distribution()
        elif choice == '3':
            tester.interactive_test()
        elif choice == '4':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试！")

if __name__ == "__main__":
    main()